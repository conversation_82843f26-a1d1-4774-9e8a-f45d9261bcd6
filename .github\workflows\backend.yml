name: Backend Docker Image

on:
  push:
    branches:
      - main
    paths:
      - "backend/**"

jobs:
  build-and-push:
    runs-on: ubuntu-latest

    steps:
      - name: Check Out Repo
        uses: actions/checkout@v3

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v1
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_ACCESS_TOKEN }}

      - name: Build and Push Image
        uses: docker/build-push-action@v2
        with:
          context: ./backend
          file: ./backend/dockerfile.backend
          push: true
          tags: 14790897/backend-handwriting:latest
          platforms: linux/amd64,linux/arm64


  # release-please-backend:
  #   runs-on: ubuntu-latest
  #   steps:
  #     - uses: actions/checkout@v2
  #     - uses: google-github-actions/release-please-action@v2
  #       with:
  #         token: ${{ secrets.GH_TOKEN }}
  #         release-type: python
  #         package-name: handwriting-backend
  #         path: ./backend
permissions:
  contents: write
