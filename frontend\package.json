{"name": "handwrite", "version": "1.3.0", "private": true, "scripts": {"serve": "vue-cli-service serve --host 0.0.0.0 --port 8080", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@microsoft/clarity": "^1.0.0", "@popperjs/core": "^2.11.8", "@sentry/vue": "^7.57.0", "axios": "^1.6.0", "bootstrap": "^5.3.0", "core-js": "^3.8.3", "sweetalert2": "^11.7.12", "vue": "^3.2.13", "vue-i18n": "^9.3.0-beta.24", "vue-router": "^4.0.3", "vuex": "^4.0.2"}, "devDependencies": {"@babel/core": "^7.26.8", "@babel/eslint-parser": "^7.26.8", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.0.3", "lint-staged": "^11.1.2", "prettier": "^2.4.1"}, "gitHooks": {"pre-commit": "lint-staged"}}