[tool.poetry]
name = "handwriting-backend"
version = "1.0.0"
description = "handwriting-backend"
authors = ["<PERSON>wei<PERSON> <EMAIL>"]
license = "MIT"

[tool.poetry.dependencies]
python = "^3.10"
blinker = "1.6.2"
cachelib = "0.10.2"
certifi = "2023.5.7"
click = "8.1.3"
colorama = "0.4.6"
deprecated = "1.2.14"
flask = "2.3.2"
flask-cors = "3.0.10"
flask-limiter = "3.3.1"
flask-session = "0.5.0"
handrightbeta = "8.6.0"
importlib-resources = "6.0.0"
itsdangerous = "2.1.2"
jinja2 = "3.1.3"
joblib = "1.3.1"
limits = "3.5.0"
load-dotenv = "0.1.0"
lxml = "4.9.2"
markdown-it-py = "3.0.0"
markupsafe = "2.1.3"
mdurl = "0.1.2"
mysql-connector-python = "8.0.33"
mysqlclient = "2.1.1"
numpy = "1.25.0"
opencv-python = "********"
ordered-set = "4.1.0"
packaging = "23.1"
pillow = "10.2.0"
protobuf = "3.20.3"
pygments = "2.15.1"
pymupdf = "1.23.5"
pymupdfb = "1.23.5"
pypandoc = "1.12"
pypdf2 = "3.0.1"
pyqt6 = "6.4.2"
pyqt6-plugins = "*******.3"
pyqt6-qt6 = "6.4.3"
pyqt6-sip = "13.5.1"
pyqt6-tools = "*******.3"
python-docx = "0.8.11"
python-dotenv = "1.0.0"
qt6-applications = "*******.3"
qt6-tools = "*******.3"
rich = "13.4.2"
schedule = "1.2.1"
scikit-learn = "1.3.0"
sentry-sdk = "1.27.1"
six = "1.16.0"
threadpoolctl = "3.1.0"
typing-extensions = "4.7.1"
urllib3 = "2.0.7"
werkzeug = "2.3.8"
wrapt = "1.15.0"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
