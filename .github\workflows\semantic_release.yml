name: Semantic Release

on:
  push:
    branches:
      - main
permissions:
      contents: write
      pages: write
      id-token: write
jobs:
  release:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3 # 检出代码
      - uses: actions/setup-node@v3 # 设置 Node.js 环境
        with:
          node-version: 21
      - run: npm install # 安装依赖 1
      - run: npx semantic-release
        env:
          GITHUB_TOKEN: ${{ secrets.GH_TOKEN }}