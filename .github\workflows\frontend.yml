name: Frontend Docker Images

on:
  push:
    branches:
      - main
    paths:
      - "frontend/**"

jobs:
  build-and-push:
    runs-on: ubuntu-latest

    steps:
      - name: Check Out Repo
        uses: actions/checkout@v3

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_ACCESS_TOKEN }}

      - name: Build and Push web Image
        uses: docker/build-push-action@v5
        with:
          context: ./frontend
          file: ./frontend/dockerfile.frontend
          push: true
          tags: 14790897/frontend-handwriting:latest
          platforms: linux/amd64,linux/arm64  # <-- 新增多平台支持

  # release-please-frontend:
  #   runs-on: ubuntu-latest
  #   steps:
  #     - uses: actions/checkout@v2
  #     - uses: google-github-actions/release-please-action@v2
  #       with:
  #         token: ${{ secrets.GH_TOKEN }}
  #         release-type: node
  #         package-name: handwriting-frontend
  #         path: ./frontend
permissions:
  contents: write
