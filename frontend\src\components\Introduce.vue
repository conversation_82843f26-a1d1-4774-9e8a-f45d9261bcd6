<template>
    <div>
        <h1>手写文字生成网站</h1>

        <p>欢迎来到我的手写文字生成网站！这个平台允许你使用现有的字体来创建模拟手写文字的图片。</p>

        <p>网址：<a href="https://handwrite.14790897.xyz">https://handwrite.14790897.xyz</a></p>

        <h2>功能</h2>

        <h3>自定义字体</h3>
        <p>你可以上传自己的字体来生成符合你需求的独特手写风格。</p>

        <h3>背景图片</h3>
        <p>上传你想要的背景图片，为你的手写文字添加个人风格。如果你没有背景图片，别担心！只需指定图片的宽度和高度，我的网站将自动为你生成带有横线的背景图片。</p>

        <h3>可调参数</h3>
        <p>你可以完全控制各种参数，如边距（上、下、左、右），字符间的随机扰动，笔画的旋转偏移，墨水的深度变化，涂改痕迹。这使你可以微调你的手写文字的外观。</p>

        <h3>从各种文件类型中提取文本</h3>
        <p>我的网站可以从各种文件类型中提取文本内容（如pdf，docs），使你能够方便地上传文本。</p>

        <h3>预览功能</h3>
        <p>我在网站的右侧添加了预览功能。这使你可以在最终确定之前方便地查看你的手写文字图片的效果。</p>

        <h3>完整图片生成</h3>
        <p>一旦你对预览满意，你可以生成一整套图片。这些图片将被方便地打包成一个 zip 文件，以便于下载。</p>

        <h3>pdf导出功能</h3>
        <p>一键生成pdf，不用再手动粘贴图片</p>

        <h2>自己搭建的方法</h2>
        <p>克隆项目，在项目目录中使用`docker-compose up -d`，默认端口为2345</p>
        <p>若要添加字体，字体文件放在项目根目录下的ttf_files中</p>

        <h2>结语</h2>
        <p>我希望你喜欢使用我的手写文字生成网站来创建你的个性化手写文字图片！</p>
    </div>
</template>
  
<script>
export default {
    name: 'IntroduceComponent',
}
</script>
  
  