name: MySql Docker image

on:
  push:
    branches:
      - main
    paths:
      - 'mysql/**'
    
jobs:
  push_to_registry:
    name: Push mysql Docker image to Docker Hub
    runs-on: ubuntu-latest
    steps:
      - name: Check out the repo
        uses: actions/checkout@v2

      - name: Log in to Docker Hub
        uses: docker/login-action@v1 
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_ACCESS_TOKEN }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v2
        with:
          context: ./mysql
          file: ./mysql/dockerfile.mysql
          push: true
          tags: 14790897/mysql-handwriting:latest
