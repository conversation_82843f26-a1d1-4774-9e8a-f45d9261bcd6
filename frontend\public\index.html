<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <meta name="description" content="手写文字生成网站，支持多种字体和背景，在线生成高质量手写文字图片和PDF，适合作业、论文、信件等场景。支持自定义字体、背景、参数调节，轻松生成个性化手写效果。">
    <meta name="keywords" content="手写文字,在线生成,图片工具,handwrite,文字生成,图片生成,手写体,作业生成,论文手写,PDF导出,自定义字体,背景图片,AI手写,手写模拟,手写作业,手写论文,手写信件,手写图片">
    <meta name="author" content="handwrite.14790897.xyz">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="https://handwrite.14790897.xyz/">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://handwrite.14790897.xyz/">
    <meta property="og:title" content="手写文字生成网站 - 在线生成手写文字图片和PDF">
    <meta property="og:description" content="支持多种字体和背景，在线生成高质量手写文字图片和PDF，适合作业、论文、信件等场景。">
    <meta property="og:image" content="/default1.png">
    <meta property="og:locale" content="zh_CN">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://handwrite.14790897.xyz/">
    <meta property="twitter:title" content="手写文字生成网站 - 在线生成手写文字图片和PDF">
    <meta property="twitter:description" content="支持多种字体和背景，在线生成高质量手写文字图片和PDF，适合作业、论文、信件等场景。">
    <meta property="twitter:image" content="/default1.png">

    <!-- 结构化数据 JSON-LD -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": "手写文字生成网站",
      "url": "https://handwrite.14790897.xyz/",
      "description": "支持多种字体和背景，在线生成高质量手写文字图片和PDF，适合作业、论文、信件等场景。",
      "image": "https://handwrite.14790897.xyz/default1.png",
      "inLanguage": "zh-CN",
      "author": {
        "@type": "Organization",
        "name": "handwrite.14790897.xyz"
      }
    }
    </script>    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json">
    
    <!-- 网站地图 -->
    <link rel="sitemap" type="application/xml" href="/sitemap.xml">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<%= BASE_URL %>favicon.ico">
    <link rel="icon" type="image/svg+xml" href="<%= BASE_URL %>icon.svg">
    <link rel="shortcut icon" href="<%= BASE_URL %>icon.svg">
    <link rel="icon" type="image/png" sizes="32x32" href="<%= BASE_URL %>favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="<%= BASE_URL %>favicon-16x16.png">
    <link rel="icon" type="image/png" sizes="96x96" href="<%= BASE_URL %>favicon-96x96.png">
    <!-- Apple Touch Icon -->
    <link rel="apple-touch-icon" sizes="180x180" href="<%= BASE_URL %>apple-touch-icon.png">

    <!-- Android Chrome Icons -->
    <link rel="icon" type="image/png" sizes="192x192" href="<%= BASE_URL %>web-app-manifest-192x192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="<%= BASE_URL %>web-app-manifest-512x512.png">
    
    <!-- <title><%= htmlWebpackPlugin.options.title %></title> -->
    <title>手写文字生成网站 - 在线生成手写文字图片和PDF | handwrite.14790897.xyz</title>
  </head>
  <body>
    <noscript>
      <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected haha-->

    <!-- Service Worker Registration -->
    <script>
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
          navigator.serviceWorker.register('/sw.js')
            .then((registration) => {
              console.log('SW registered: ', registration);
            })
            .catch((registrationError) => {
              console.log('SW registration failed: ', registrationError);
            });
        });
      }
    </script>
  </body>
</html>
